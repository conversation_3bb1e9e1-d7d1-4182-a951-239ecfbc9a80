"use client";

import { createContext, useContext, useState } from "react";

// @context type
export type User = {
  uid: string;
  username: string;
  email: string;
  role: string;
};
type Response = {
  success: boolean;
  message: string;
  data: User;
};
type ActionRreturn = {
  success: boolean;
  redirectTo: string;
};
type AuthContextType = {
  user: User;
  loading: boolean;
  login: (
    user: Pick<User, "email"> & { password: string }
  ) => Promise<ActionRreturn>;
  register: (user: Omit<User, "role"> & { password: string }) => void;
  logout: () => void;
  keepLogin: () => Promise<ActionRreturn>;
};

// @initial state
const INITIAL_STATE: AuthContextType = {
  user: {
    uid: "",
    username: "",
    email: "",
    role: "",
  },
  loading: true,
  login: () => Promise.resolve({ success: false, redirectTo: "" }),
  register: () => {},
  logout: () => {},
  keepLogin: () => Promise.resolve({ success: false, redirectTo: "" }),
};

// @create context
const AuthContext = createContext<AuthContextType>(INITIAL_STATE);
const useAuth = () => useContext(AuthContext);

// @provider
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User>(INITIAL_STATE.user);
  const [loading, setLoading] = useState(true);

  // @handle login
  const login = async (user: { email: string; password: string }) => {
    try {
      if (!loading) setLoading(true);
      // handle login
      const response = await fetch("http://localhost:2000/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(user),
      });

      // @handle response and get token from Authorization header
      const token = response.headers.get("Authorization") || "";
      const data = (await response.json()) as Response;

      // @set user
      setUser(data.data);

      // @save toke to local storage
      localStorage.setItem("auth-token", token);

      return {
        success: true,
        redirectTo: "/",
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        redirectTo: "",
      };
    } finally {
      setLoading(false);
    }
  };

  // keep login -> rehydrate user data everytime user refresh page
  const keepLogin = async () => {
    try {
      if (!loading) setLoading(true);
      const token = localStorage.getItem("auth-token");
      if (!token) return { success: false, redirectTo: "/login" };

      // rehydrate user data from server
      const response = await fetch(
        "http://localhost:2000/api/auth/keep-login",
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = (await response.json()) as Response;
      if (!data.success) return { success: false, redirectTo: "/login" };
      setUser(data.data);
      return {
        success: true,
        redirectTo: "/",
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        redirectTo: "/login",
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem("auth-token");
    setUser(INITIAL_STATE.user);
  };

  console.log("user:", user);
  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        register: () => {},
        logout,
        keepLogin,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export { AuthContext, AuthProvider, useAuth };
