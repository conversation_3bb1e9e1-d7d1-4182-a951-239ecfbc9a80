"user server";
import cookies from "next/headers";

type User = {
  uid: string;
  username: string;
  email: string;
  role: string;
  verified: boolean;
  active: boolean;
};
type Response = {
  success: boolean;
  message: string;
  data: User;
  count: number;
};

export async function Login(user: {
  email: string;
  password: string;
}): Promise<Response | null> {
  try {
    const response = await fetch("http://localhost:2000/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(user),
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(error);
    return null;
  }
}
