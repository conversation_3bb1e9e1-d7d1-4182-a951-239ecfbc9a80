"use client";

import { useEffect } from "react";
import Link from "next/link";
import { LogOut, LogIn, Shield<PERSON>heck, Loader } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context/auth.context";

export default function Home() {
  const { user, loading, keepLogin, logout } = useAuth();

  // @side-effect
  useEffect(() => {
    keepLogin();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading)
    return (
      <main className="flex items-center justify-center min-h-screen ">
        <div className="bg-white p-8 flex items-center gap-2 rounded-md shadow-md">
          <Loader className="animate-spin" />
          <p>Loading...</p>
        </div>
      </main>
    );

  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <h1 className="text-2xl font-bold mb-4">Wellcome to My Website</h1>
        {user.username && <p>Username: {user.username}</p>}
        {user.email && <p>Email: {user.email}</p>}
        <Link href="/admin">
          <Button className="cursor-pointer w-full bg-blue-700">
            <ShieldCheck />
            Go to Admin Portal
          </Button>
        </Link>
        {user && user.uid && (
          <Button className="cursor-pointer bg-red-700" onClick={logout}>
            <LogOut />
            Logout
          </Button>
        )}
        {!user.uid && (
          <Link href="/login">
            <Button className="cursor-pointer w-full">
              <LogIn />
              Login
            </Button>
          </Link>
        )}
      </section>
    </main>
  );
}
