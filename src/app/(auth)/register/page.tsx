"use client";

import Link from "next/link";
import { LogIn } from "lucide-react";
import { Button } from "@/components/ui/button";
import { BackButton } from "@/components/feature/back-button";
import { Input } from "@/components/ui/input";
import useURLQueryParams from "@/hooks/useURLQueryParams";

export default function Register() {
  const { set, get } = useURLQueryParams();
  const username = get("username") || "";
  const email = get("email") || "";
  const password = get("password") || "";
  const confirmPassword = get("confirmPassword") || "";

  const handleRegister = () => {
    // Handle register logic here
  };

  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <BackButton />
        <h1 className="capitalize text-2xl font-bold mb-4">Register</h1>
        <Input
          value={username}
          type="text"
          placeholder="Username"
          onChange={(e) => set("username", e.target.value)}
        />
        <Input
          value={email}
          type="email"
          placeholder="Email"
          onChange={(e) => set("email", e.target.value)}
        />
        <Input
          value={password}
          type="password"
          placeholder="Password"
          onChange={(e) => set("password", e.target.value)}
        />
        <Input
          value={confirmPassword}
          type="confirmPassword"
          placeholder="Confirm Password"
          onChange={(e) => set("confirmPassword", e.target.value)}
        />
        <Button
          size="lg"
          className="bg-gray-800 text-white cursor-pointer"
          onClick={handleRegister}
        >
          <LogIn />
          Register
        </Button>
        <p className="text-sm">
          Already have an account?{" "}
          <Link href="/login" className="underline text-blue-700">
            Login here
          </Link>
        </p>
      </section>
    </main>
  );
}
