"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { LogIn, Loader } from "lucide-react";
import clsx from "clsx";
import { Button } from "@/components/ui/button";
import { BackButton } from "@/components/feature/back-button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/context/auth.context";
import useURLQueryParams from "@/hooks/useURLQueryParams";

export default function Login() {
  const router = useRouter();
  const { set, get } = useURLQueryParams();
  const { login, loading } = useAuth();
  const email = get("email") || "";
  const password = get("password") || "";

  const handleLogin = async () => {
    // @todo: do validation login
    // @sent to server
    const { success, redirectTo } = await login({ email, password });
    if (success) {
      router.replace(redirectTo);
    }
  };

  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <BackButton />
        <h1 className="capitalize text-2xl font-bold mb-4">Login</h1>
        <Input
          value={email}
          type="email"
          placeholder="Email"
          onChange={(e) => set("email", e.target.value)}
        />
        <Input
          value={password}
          type="password"
          placeholder="Password"
          onChange={(e) => set("password", e.target.value)}
        />
        <Button
          size="lg"
          className={clsx(
            "text-white cursor-pointer",
            loading && "bg-gray-400"
          )}
          onClick={handleLogin}
          disabled={loading}
        >
          {loading ? <Loader className="animate-spin" /> : <LogIn />}
          Login
        </Button>
        <p className="text-sm">
          Don&apos;t have an account?{" "}
          <Link href="/register" className="underline text-blue-700">
            Register here
          </Link>
        </p>
      </section>
    </main>
  );
}
