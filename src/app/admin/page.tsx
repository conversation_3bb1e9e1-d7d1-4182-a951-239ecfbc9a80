"use client";

import Link from "next/link";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader } from "lucide-react";
import { useAuth } from "@/context/auth.context";
import { Button } from "@/components/ui/button";

export default function Admin() {
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (user.role !== "admin") {
      router.replace("/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (user.role !== "admin")
    return (
      <main className="flex items-center justify-center min-h-screen ">
        <div className="bg-white p-8 flex items-center gap-2 rounded-md shadow-md">
          <Loader className="animate-spin" />
          <p>Loading...</p>
        </div>
      </main>
    );

  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <div className="text-2xl font-bold mb-4 flex items-center gap-2">
          <ShieldCheck /> <p>Wellcome to Admin Portal</p>
        </div>
        <Link href="/">
          <Button className="cursor-pointer w-full">
            <ArrowLeft />
            Back to home
          </Button>
        </Link>
      </section>
    </main>
  );
}
