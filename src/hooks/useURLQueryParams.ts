"use client";
import { useSearchParams, usePathname, useRouter } from "next/navigation";

// @types
type URLQueryParams = {
  set: (key: string, value: string) => void;
  get: (key: string) => string | null;
  delete: (key: string) => void;
  forEach: (callbackfn: (value: string, key: string) => void) => void;
  keys: () => IterableIterator<string>;
  values: () => IterableIterator<string>;
  entries: () => IterableIterator<[string, string]>;
};

export default function useURLQueryParams(): URLQueryParams {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  // @build new query
  const query = new URLSearchParams(searchParams);

  // @return
  return Object.freeze({
    // @methods: set query -> update url
    set: (key: string, value: string): void => {
      query.set(key, value);
      router.replace(`${pathname}?${query.toString()}`, { scroll: false });
    },
    // @methods: get query
    get: (key: string): string | null => {
      return query.get(key);
    },
    // @methods: delete query
    delete: (key: string): void => {
      query.delete(key);
      router.replace(`${pathname}?${query.toString()}`);
    },
    // @methods: iterate query
    forEach: (callbackfn: (value: string, key: string) => void): void => {
      query.forEach(callbackfn);
    },
    // @methods: get query keys
    keys: (): IterableIterator<string> => {
      return query.keys();
    },
    // @methods: get query values
    values: (): IterableIterator<string> => {
      return query.values();
    },
    // @methods: get query entries
    entries: (): IterableIterator<[string, string]> => {
      return query.entries();
    },
  });
}
